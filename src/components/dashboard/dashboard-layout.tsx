"use client";

import React, { useState, useEffect } from "react";
import { useSession } from "next-auth/react";
import { useRouter, usePathname } from "next/navigation";
import Link from "next/link";
import { 
  Home, 
  Settings, 
  CreditCard, 
  Users, 
  FileCode, 
  GitFork,
  LogOut,
  Search,
  Plus,
  ChevronUp,
  ChevronDown,
  BookOpen,
  HelpCircle,
  User,
  Lock,
  GitBranch,
  LayoutDashboard,
  BarChart2,
  DollarSign,
  Plug
} from "lucide-react";
import { Button } from "@/src/components/ui/button";
import { signOut } from "next-auth/react";
import { OrganizationSelector } from "@/src/components/workspace/organization-selector";
import { Input } from "@/src/components/ui/input";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/src/components/ui/select";
import {
  Sidebar,
  SidebarContent,
  SidebarFooter,
  SidebarGroup,
  SidebarGroupContent,
  SidebarGroupLabel,
  SidebarHeader,
  SidebarMenu,
  SidebarMenuItem,
  SidebarMenuButton,
  SidebarProvider,
  SidebarRail
} from "@/src/components/ui/sidebar";
import {
  Avatar,
  AvatarFallback,
  AvatarImage,
} from "@/src/components/ui/avatar";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/src/components/ui/dropdown-menu";
import {
  Breadcrumb,
  BreadcrumbItem,
  BreadcrumbLink,
  BreadcrumbList,
  BreadcrumbPage,
  BreadcrumbSeparator,
} from "@/src/components/ui/breadcrumb";

// Navigation Items
const navigationItems = [
  { title: "Home", url: "/dashboard", icon: Home },
  { title: "Repositories", url: "/dashboard/repositories", icon: GitBranch },
  { title: "Dashboard", url: "/dashboard/overview", icon: LayoutDashboard, locked: true },
  { title: "Integrations", url: "/dashboard/integrations", icon: Plug, locked: true },
  { title: "Reports", url: "/dashboard/reports", icon: BarChart2, locked: true },
  { title: "Learnings", url: "/dashboard/learnings", icon: BookOpen },
  { title: "Organization Settings", url: "/dashboard/settings", icon: Settings },
  { title: "Subscription", url: "/dashboard/subscription", icon: DollarSign },
];

const bottomItems = [
  { title: "Docs", url: "/docs", icon: BookOpen },
  { title: "Support", url: "/support", icon: HelpCircle },
];

// Main Dashboard Layout
export function DashboardLayout({ children }: { children: React.ReactNode }) {
  const { data: session, status } = useSession();
  const router = useRouter();
  const pathname = usePathname();
  
  const handleSignOut = () => {
    signOut();
  };

  useEffect(() => {
    if (status === "loading") return;

    if (!session) {
      router.push("/login");
    }
  }, [session, status, router]);

  if (status === "loading") {
    return (
      <div className="flex h-screen items-center justify-center bg-gray-50">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-[#00617b] mx-auto mb-4"></div>
          <p className="text-gray-500">Loading...</p>
        </div>
      </div>
    );
  }

  if (!session) {
    return null;
  }

  return (
    <SidebarProvider>
      <div className="min-h-screen flex w-full bg-gray-50">
        {/* Sidebar */}
        <Sidebar className="shadow-sm border-r border-gray-200 w-64 flex-shrink-0 bg-white">
          <SidebarHeader className="border-b border-gray-200 pb-4 space-y-4">
            <div className="px-2">
              <OrganizationSelector />
            </div>
          </SidebarHeader>

          <SidebarContent className="px-2 overflow-y-auto">
            {/* Main Navigation */}
            <SidebarGroup>
              <SidebarGroupLabel className="text-xs font-semibold text-gray-400 uppercase tracking-wider px-2 mb-2">
                Navigation
              </SidebarGroupLabel>
              <SidebarGroupContent>
                <SidebarMenu className="space-y-1">
                  {navigationItems.map((item) => (
                    <SidebarMenuItem key={item.title}>
                      <SidebarMenuButton
                        asChild
                        isActive={pathname === item.url || (item.url !== "/dashboard" && pathname.startsWith(item.url))}
                        className="w-full justify-start rounded-lg px-3 py-2 text-sm font-medium transition-all text-gray-600 hover:text-gray-800 hover:bg-gray-50 data-[active=true]:bg-[#00617b]/10 data-[active=true]:text-[#00617b] data-[active=true]:font-semibold"
                      >
                        <Link href={item.url} className="flex items-center w-full">
                          <item.icon className="h-4 w-4 mr-3" />
                          <span className="flex-1">{item.title}</span>
                          {item.locked && <Lock className="h-4 w-4 text-gray-400" />}
                        </Link>
                      </SidebarMenuButton>
                    </SidebarMenuItem>
                  ))}
                </SidebarMenu>
              </SidebarGroupContent>
            </SidebarGroup>

            {/* Resources */}
            <SidebarGroup className="mt-8">
              <SidebarGroupLabel className="text-xs font-semibold text-gray-400 uppercase tracking-wider px-2 mb-2">
                Resources
              </SidebarGroupLabel>
              <SidebarGroupContent>
                <SidebarMenu className="space-y-1">
                  {bottomItems.map((item) => (
                    <SidebarMenuItem key={item.title}>
                      <SidebarMenuButton
                        asChild
                        className="w-full justify-start rounded-lg px-3 py-2 text-sm font-medium transition-all text-gray-600 hover:text-gray-800 hover:bg-gray-50"
                      >
                        <Link href={item.url}>
                          <item.icon className="h-4 w-4 mr-3" />
                          <span>{item.title}</span>
                        </Link>
                      </SidebarMenuButton>
                    </SidebarMenuItem>
                  ))}
                </SidebarMenu>
              </SidebarGroupContent>
            </SidebarGroup>
          </SidebarContent>

          <SidebarFooter>
            <SidebarMenu>
              <SidebarMenuItem>
                <DropdownMenu>
                  <DropdownMenuTrigger asChild>
                    <SidebarMenuButton
                      size="lg"
                      className="data-[state=open]:bg-sidebar-accent data-[state=open]:text-sidebar-accent-foreground"
                    >
                      <Avatar className="h-8 w-8 rounded-lg">
                        <AvatarImage src={session?.user?.image || ""} alt={session?.user?.name || ""} />
                        <AvatarFallback className="rounded-lg">
                          {session?.user?.name?.charAt(0) || "U"}
                        </AvatarFallback>
                      </Avatar>
                      <div className="grid flex-1 text-left text-sm leading-tight">
                        <span className="truncate font-semibold text-gray-900">{session?.user?.name || "User"}</span>
                        <span className="truncate text-xs text-gray-600">{session?.user?.email || "<EMAIL>"}</span>
                      </div>
                    </SidebarMenuButton>
                  </DropdownMenuTrigger>
                  <DropdownMenuContent
                    className="w-[--radix-dropdown-menu-trigger-width] min-w-56 rounded-lg"
                    side="bottom"
                    align="end"
                    sideOffset={4}
                  >
                    <DropdownMenuLabel className="p-0 font-normal">
                      <div className="flex items-center gap-2 px-1 py-1.5 text-left text-sm">
                        <Avatar className="h-8 w-8 rounded-lg">
                          <AvatarImage src={session?.user?.image || ""} alt={session?.user?.name || ""} />
                          <AvatarFallback className="rounded-lg">
                            {session?.user?.name?.charAt(0) || "U"}
                          </AvatarFallback>
                        </Avatar>
                        <div className="grid flex-1 text-left text-sm leading-tight">
                          <span className="truncate font-semibold text-gray-900">{session?.user?.name || "User"}</span>
                          <span className="truncate text-xs text-gray-600">{session?.user?.email || "<EMAIL>"}</span>
                        </div>
                      </div>
                    </DropdownMenuLabel>
                    <DropdownMenuSeparator />
                    <DropdownMenuItem asChild>
                      <Link href="/dashboard/profile" className="flex items-center text-gray-700 hover:!text-gray-900 hover:!bg-gray-50 cursor-pointer">
                        <User className="mr-2 h-4 w-4 text-gray-500 hover:!text-gray-700" />
                        Profile
                      </Link>
                    </DropdownMenuItem>
                    <DropdownMenuSeparator />
                    <DropdownMenuItem onClick={handleSignOut} className="text-gray-700 hover:!text-gray-900 hover:!bg-gray-50 cursor-pointer">
                      <LogOut className="mr-2 h-4 w-4 text-gray-500 hover:!text-gray-700" />
                      Sign out
                    </DropdownMenuItem>
                  </DropdownMenuContent>
                </DropdownMenu>
              </SidebarMenuItem>
            </SidebarMenu>
          </SidebarFooter>
        </Sidebar>

        {/* Main Content */}
        <div className="flex-1 flex flex-col bg-gray-50 min-w-0">
          {/* Page Content */}
          <main className="flex-1 p-6 min-w-0 overflow-auto bg-gray-50">
            <div className="max-w-7xl mx-auto">
              {children}
            </div>
          </main>
        </div>
      </div>
    </SidebarProvider>
  );
}
